include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# PASIV-API: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
pasiv_api_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "pasiv-api"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pasiv-api/**
        - ./*

# Note: pasiv-api doesn't have a Helm chart, so no deployment jobs are needed
# This service is likely a library/dependency for other services

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - pasiv-api/**

pasiv_api_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pasiv-api"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules
