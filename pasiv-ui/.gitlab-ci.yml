include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# PASIV-UI: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
pasiv_ui_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "pasiv-ui"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_build_and_push  # UI service uses docker_build_and_push instead of gradle
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pasiv-ui/**
        - ./*

pasiv_ui_helm_kubeval_testing_develop:
  stage: test
  needs: 
    - job: pasiv_ui_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    SERVICE_NAME: "pasiv-ui"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pasiv-ui/**
        - charts/pasiv-ui/**

pasiv_ui_deploy_chart_develop:
  stage: deploy
  needs:
    - pasiv_ui_helm_kubeval_testing_develop
    - job: pasiv_ui_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "pasiv-ui"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pasiv-ui/**
        - charts/pasiv-ui/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - pasiv-ui/**

pasiv_ui_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pasiv-ui"
  extends:
    - .docker_build_and_push  # UI service uses docker_build_and_push instead of gradle
  <<: *tag_rules

pasiv_ui_helm_kubeval_testing_tag:
  stage: test
  needs:
    - pasiv_ui_build_tag
  variables:
    SERVICE_NAME: "pasiv-ui"
  extends:
    - .validate_helm_template
  <<: *tag_rules

pasiv_ui_deploy_chart_tag:
  stage: deploy
  needs:
    - pasiv_ui_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pasiv-ui"
  extends:
    - .deploy_helm_template
  <<: *tag_rules

pasiv_ui_deploy_prod:
  stage: deploy
  needs:
    - pasiv_ui_deploy_chart_tag
  variables:
    STAGE: "prod"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pasiv-ui"
    NAMESPACE: "$NAMESPACE-prod"
  extends:
    - .deploy_helm_template
  when: manual
  <<: *tag_rules
