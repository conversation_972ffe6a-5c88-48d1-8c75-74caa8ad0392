# Development environment specific values for pasiv-gate-private
# Only contains values that differ from default values.yaml

replicaCount: 1

image:
  pullPolicy: Always
  tag: "dev"

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 128Mi

env:
  keycloak:
    client_id: "test-auth"
    realm:
      url: "https://dev-auth.sbertroika.tech/realms/test-asop"
  tms_gate_service: "tms-gate.tms.svc.cluster.local:5005"
